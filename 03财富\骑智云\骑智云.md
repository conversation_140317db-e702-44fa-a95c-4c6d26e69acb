

- [ ] 搭建 mcp server
	- [x] [从零实现一个MCP](https://www.toutiao.com/article/7485648982757229095/?app=news_article&category_new=text_inner_flow&chn_id=-3&group_id=7485648982757229095&req_id=20250410225204086CED11F51CBF174415&req_id_new=20250410225204086CED11F51CBF174415&share_did=MS4wLjACAAAAvU_50qlBvKW-uVNgkOSC1LmFc43D97w9jIlxDGdaST-3i6g3yh_IKYyFuaBLGnPb&share_token=3c6cdab9-ccda-472e-b7e4-58df547b3be5&share_uid=MS4wLjABAAAA9-t0JQF9zf4syccrvaE1aEGhNOwtey9xy2NospzRWKA&timestamp=1744296724&tt_from=copy_link&use_new_style=1&utm_campaign=client_share&utm_medium=toutiao_android&utm_source=copy_link&source=m_redirect) ✅ 2025-04-11
	- [x] https://www.youtube.com/watch?v=McNRkd5CxFY  Cline、Cursor 上使用MCP——MCP的本质就是调用电脑上的相关程序，调用方法写在了配置文件里面，程序一般是nodeJS或Python，一般出错大概都是权限不够，或者环境配置问题 ✅ 2025-04-12

